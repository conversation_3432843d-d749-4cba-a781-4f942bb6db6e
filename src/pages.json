{
  "entryPagePath": "pages/homepage/index2",
  "easycom": {
    "autoscan": true,
    // 注意一定要放在custom里，否则无效，https://ask.dcloud.net.cn/question/131175
    "custom": {
      "^u--(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^z-(.*)": "@zebra-ui/swiper/components/z-$1/z-$1.vue"
    }
  },
  "pages": [
    /* tabber start */
    {
      "path": "pages/homepage/index2",
      "name": "Homepage",
      "needLogin": false
    },
    /**
    {
      "path": "pages/tabulation/list",
      "name": "Tabulation",
      "needLogin": false,
      "style": {
        "disableScroll": true
      }
    },
    **/
    {
      "path": "pages/calendar/index",
      "name": "Calendar",
      "needLogin": false,
      "style": {
        "disableScroll": false
      }
    },
    {
      "path": "pages/collection/list",
      "name": "CollectionList",
      "needLogin": false,
      "style": {
        "disableScroll": true
      }
    },
    {
      "path": "pages/account/personal",
      "name": "Personal",
      "needLogin": false,
      "style": {
        "disableScroll": true
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/sub",
      "pages": [
        /*  account start */
        {
          "path": "account/login",
          "name": "Login",
          "needLogin": false,
          "style": {
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "account/register",
          "name": "Register",
          "needLogin": false,
          "style": {
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "account/agreement",
          "name": "Agreement",
          "needLogin": false,
          "style": {
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "account/userInfo",
          "name": "UserInfo",
          "needLogin": true
        },
        {
          "path": "account/avatarSel",
          "name": "AvatarSel",
          "needLogin": true
        },
        {
          "path": "account/setting",
          "name": "Setting",
          "needLogin": true
        },
        {
          "path": "account/settings",
          "name": "Settings",
          "needLogin": true
        },
        {
          "path": "account/authentication",
          "name": "Authentication",
          "needLogin": true
        },
        {
          "path": "account/editPwd",
          "name": "EditPwd",
          "needLogin": true
        },
        {
          "path": "account/editPhone",
          "name": "EditPhone",
          "needLogin": true
        },
        {
          "path": "account/writeoff",
          "name": "Writeoff",
          "needLogin": true
        },
        /* search start */
        {
          "path": "tabulation/search",
          "name": "Search",
          "needLogin": false,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "tabulation/result",
          "name": "Result",
          "needLogin": false,
          "style": {
            "disableScroll": true
          }
        },
        /* detail start */
        {
          "path": "detail/theater",
          "name": "TheaterDetail",
          "needLogin": false
        },
        {
          "path": "detail/repertoire",
          "name": "RepertoireDetail",
          "needLogin": false,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "detail/showcase",
          "name": "Showcase",
          "needLogin": false,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "detail/dynamic",
          "name": "DynamicDetail",
          "needLogin": false,
          "style": {
            "disableScroll": true
          }
        },
        /* scan start */
        {
          "path": "scan/result",
          "name": "ScanResult",
          "needLogin": true
        },
        {
          "path": "scan/receive",
          "name": "Receive",
          "needLogin": true
        },
        {
          "path": "scan/upgrade",
          "name": "Upgrade",
          "needLogin": true
        },
        {
          "path": "scan/ticketSave",
          "name": "TicketSave",
          "needLogin": true
        },
        {
          "path": "scan/avatarSave",
          "name": "AvatarSave",
          "needLogin": true
        },
        {
          "path": "scan/actor",
          "name": "AddActor",
          "needLogin": true
        },
        /* collection start */
        {
          "path": "collection/detail",
          "name": "CollectionDetail",
          "needLogin": false
        },
        /* message start */
        {
          "path": "message/list",
          "name": "MsgList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "message/detail",
          "name": "MsgDetail",
          "needLogin": true
        },
        /* ticket start */
        {
          "path": "ticket/list",
          "name": "TicketList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "ticket/detail",
          "name": "TicketDetail",
          "needLogin": true
        },
        {
          "path": "ticket/group",
          "name": "TicketGroup",
          "needLogin": true
        },
        {
          "path": "ticket/groupEdit",
          "name": "TicketGroupEdit",
          "needLogin": true
        },
        {
          "path": "ticket/selTicket",
          "name": "SelTicket",
          "needLogin": true
        },
        /* avatar start */
        {
          "path": "avatar/list",
          "name": "AvatarList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "avatar/detail",
          "name": "AvatarDetail",
          "needLogin": true
        },
        /* badge start */
        {
          "path": "badge/list",
          "name": "BadgeList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "badge/detail",
          "name": "BadgeDetail",
          "needLogin": true
        },
        {
          "path": "badge/share",
          "name": "BadgeShare",
          "needLogin": true
        },
        /* follow start */
        {
          "path": "follow/list",
          "name": "FollowList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        /* order start */
        {
          "path": "order/list",
          "name": "OrderList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "order/detail",
          "name": "OrderDetail",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        /* comment start */
        {
          "path": "comment/list",
          "name": "CommentList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "comment/detail",
          "name": "CommentDetail",
          "needLogin": true
        },
        /* medal start */
        {
          "path": "medal/list",
          "name": "MedalList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        /* Rank start */
        {
          "path": "rank/index",
          "name": "Rank",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "rank/list",
          "name": "RankList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "rank/edit",
          "name": "RankEdit",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        /* history start */
        {
          "path": "history/list",
          "name": "HistoryList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "history/comment",
          "name": "HistoryComment",
          "needLogin": true,
          "style": {
            "disableScroll": false
          }
        },
        /* interaction start */
        {
          "path": "interaction/list",
          "name": "InteractionList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        /* mass start */
        {
          "path": "mass/list",
          "name": "MassList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        {
          "path": "mass/detail",
          "name": "MassDetail",
          "needLogin": true
        },
        /* ask start */
        {
          "path": "ask/list",
          "name": "AskList",
          "needLogin": true,
          "style": {
            "disableScroll": true
          }
        },
        /* calendar start */
        {
          "path": "calendar/add-material",
          "name": "AddMaterial",
          "needLogin": true
        },
        {
          "path": "calendar/material-detail",
          "name": "MaterialDetail",
          "needLogin": true
        },
        {
          "path": "calendar/material-manage",
          "name": "MaterialManage",
          "needLogin": true
        },
        /* other start */
        {
          "path": "city/select",
          "name": "CitySelect",
          "needLogin": false
        },
        {
          "path": "camera/index",
          "name": "Camera",
          "needLogin": true
        },
        {
          "path": "error/404",
          "name": "404",
          "needLogin": false
        },
        /* ai start */
        {
          "path": "ai/searchResult",
          "name": "AISearchResult",
          "needLogin": false,
          "style": {
            "disableScroll": true
          }
        }
      ]
    }
  ],
  "tabBar": {
    "custom": true,
    "color": "#9592A0",
    "selectedColor": "#9C75D3",
    "backgroundColor": "#07011D",
    "list": [
      {
        "pagePath": "pages/homepage/index2",
        "text": "首页"
      },
      {
        "pagePath": "pages/calendar/index",
        "text": "日历"
      },
      {
        "pagePath": "pages/collection/list",
        "text": "商品"
      },
      {
        "pagePath": "pages/account/personal",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "white",
    "navigationStyle": "custom",
    "backgroundColorTop": "#1E1733",
    "backgroundColorBottom": "#0A0713",
    "backgroundColor": "#FFFFFF"
  }
}
