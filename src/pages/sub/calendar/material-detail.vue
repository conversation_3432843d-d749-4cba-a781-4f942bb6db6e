<template>
  <view class="page-container">
    <u-navbar class="navbar w-full shrink-0" bgColor="#ffffff" placeholder>
      <template #left>
        <view class="flex items-center justify-between w-full px-6">
          <view class="nav-left">
            <text class="nav-back-icon fas fa-chevron-left" @click="goBack"></text>
            <text class="nav-title">物料详情</text>
          </view>
          <view v-if="isOwner" class="nav-right">
            <text class="nav-action-icon fas fa-edit" @click="editMaterial"></text>
            <text class="nav-action-icon fas fa-trash delete-icon" @click="deleteMaterial"></text>
          </view>
        </view>
      </template>
    </u-navbar>

    <scroll-view scroll-y class="content-container">
      <!-- 物料信息卡片 -->
      <view class="material-card">
        <!-- 物料图片 -->
        <view class="image-container">
          <image :src="materialInfo.image" class="material-image" />
        </view>

        <view class="card-content">
          <!-- 基本信息 -->
          <view class="basic-info">
            <text class="material-title">{{ materialInfo.title }}</text>
            <view class="meta-info">
              <view class="meta-item">
                <text class="fas fa-clock meta-icon"></text>
                <text class="meta-text">{{ materialInfo.publishTime }}</text>
              </view>
              <view class="meta-item">
                <text class="fas fa-map-marker-alt meta-icon"></text>
                <text class="meta-text">{{ materialInfo.location }}</text>
              </view>
            </view>
          </view>

          <!-- 领取条件 -->
          <view class="condition-section">
            <text class="section-title">领取条件</text>
            <text class="condition-text">{{ materialInfo.condition }}</text>
          </view>

          <!-- 发布者信息 -->
          <view class="publisher-card">
            <view class="publisher-info">
              <image :src="materialInfo.publisher.avatar" class="publisher-avatar" />
              <view class="publisher-details">
                <text class="publisher-name">{{ materialInfo.publisher.name }}</text>
                <text class="publisher-role">发布者</text>
              </view>
            </view>
            <view v-if="!isOwner" class="follow-button">
              <text class="follow-text">关注</text>
            </view>
          </view>

          <!-- 想领统计 -->
          <view class="want-section">
            <view class="want-header">
              <text class="want-title">想领名单 ({{ wantList.length }})</text>
              <text class="view-all-text" @click="showAllWantList">查看全部</text>
            </view>

            <view v-if="wantList.length > 0" class="want-list">
              <view class="avatar-group">
                <image v-for="user in wantList.slice(0, 5)" :key="user.id" :src="user.avatar" class="want-avatar" />
                <view v-if="wantList.length > 5" class="more-avatar">
                  <text class="more-count">+{{ wantList.length - 5 }}</text>
                </view>
              </view>
              <text class="want-count-text">等{{ wantList.length }}人想领</text>
            </view>

            <view v-else class="empty-want">
              <text class="empty-text">暂无人想领</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="action-bar">
        <view v-if="!isOwner" class="action-buttons">
          <view class="want-button-container">
            <view class="want-button" :class="{ active: hasWanted }" @click="toggleWant">
              <text class="want-button-text">{{ hasWanted ? '取消想领' : '我想领' }}</text>
            </view>
          </view>
          <view class="share-button" @click="shareMaterial">
            <text class="fas fa-share share-icon"></text>
          </view>
        </view>

        <view v-else class="owner-notice">
          <text class="owner-text">这是您发布的物料</text>
        </view>
      </view>
    </scroll-view>

    <!-- 想领名单弹窗 -->
    <u-popup v-model:show="showWantListModal" mode="bottom" :round="20" @close="showWantListModal = false">
      <view class="modal-container">
        <view class="modal-header">
          <text class="modal-title">想领名单</text>
        </view>

        <scroll-view scroll-y class="modal-content">
          <view v-for="user in wantList" :key="user.id" class="user-item">
            <view class="user-info">
              <image :src="user.avatar" class="user-avatar" />
              <view class="user-details">
                <text class="user-name">{{ user.name }}</text>
                <text class="want-time">{{ user.wantTime }}</text>
              </view>
            </view>
            <!-- <view class="follow-button-small">
              <text class="follow-text-small">关注</text>
            </view> -->
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'

// 状态管理
const statusBarHeight = ref(0)
const materialId = ref('')
const showWantListModal = ref(false)
const hasWanted = ref(false)

// 模拟数据 - 便于后续替换为真实API
const mockMaterialData = {
  '1': {
    id: '1',
    title: '《狮子王》限量版海报',
    image: 'https://picsum.photos/400/300?random=301',
    publishTime: '2024-07-28 14:30',
    location: '上海大剧院门厅',
    condition: '需要持有当日演出票据，每人限领一份。领取时间为演出开始前30分钟至演出结束后30分钟。海报数量有限，先到先得。',
    publisher: {
      id: '1',
      name: '上海大剧院官方',
      avatar: 'https://picsum.photos/100/100?random=101'
    }
  },
  '2': {
    id: '2',
    title: '音乐剧纪念品套装',
    image: 'https://picsum.photos/400/300?random=302',
    publishTime: '2024-07-30 19:00',
    location: '剧院商店',
    condition: '购买任意价位演出票即可免费领取一套纪念品，包含徽章、贴纸、明信片等。每张票限领一套。',
    publisher: {
      id: '2',
      name: '剧迷小组长',
      avatar: 'https://picsum.photos/100/100?random=102'
    }
  }
}

const mockWantListData = {
  '1': [
    {
      id: '1',
      name: '音乐剧爱好者小王',
      avatar: 'https://picsum.photos/100/100?random=201',
      wantTime: '2小时前'
    },
    {
      id: '2',
      name: '戏剧迷小李',
      avatar: 'https://picsum.photos/100/100?random=202',
      wantTime: '3小时前'
    },
    {
      id: '3',
      name: '看剧小达人',
      avatar: 'https://picsum.photos/100/100?random=203',
      wantTime: '5小时前'
    },
    {
      id: '4',
      name: '剧场常客',
      avatar: 'https://picsum.photos/100/100?random=204',
      wantTime: '1天前'
    },
    {
      id: '5',
      name: '演出收藏家',
      avatar: 'https://picsum.photos/100/100?random=205',
      wantTime: '1天前'
    },
    {
      id: '6',
      name: '音乐剧新手',
      avatar: 'https://picsum.photos/100/100?random=206',
      wantTime: '2天前'
    }
  ],
  '2': [
    {
      id: '7',
      name: '剧迷团长',
      avatar: 'https://picsum.photos/100/100?random=207',
      wantTime: '1小时前'
    },
    {
      id: '8',
      name: '周末看剧人',
      avatar: 'https://picsum.photos/100/100?random=208',
      wantTime: '4小时前'
    }
  ]
}

// 数据
const materialInfo = ref(mockMaterialData['1'])
const wantList = ref(mockWantListData['1'] || [])

// 计算属性
const isOwner = computed(() => {
  // 判断是否为发布者
  return false
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const toggleWant = () => {
  hasWanted.value = !hasWanted.value
  uni.showToast({
    title: hasWanted.value ? '已登记想领' : '已取消想领',
    icon: 'success'
  })
}

const showAllWantList = () => {
  showWantListModal.value = true
}

const shareMaterial = () => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

const editMaterial = () => {
  uni.navigateTo({
    url: `/pages/sub/calendar/add-material?id=${materialId.value}&mode=edit`
  })
}

const deleteMaterial = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个物料信息吗？',
    success: res => {
      if (res.confirm) {
        // 删除逻辑
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}

onLoad(options => {
  materialId.value = options?.id || ''
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0

  // 加载物料详情
  loadMaterialDetail()
})

const loadMaterialDetail = () => {
  // 模拟加载数据
  const data = mockMaterialData[1]
  const wants = mockWantListData[1]

  if (data) {
    materialInfo.value = data
    wantList.value = wants || []
  } else {
    uni.showToast({
      title: '物料不存在',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }

  // 模拟检查是否已经想领
  hasWanted.value = Math.random() > 0.5
}
</script>

<style lang="scss" scoped>
/* 页面容器 */
.page-container {
  height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar-container {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-back-icon {
  font-size: 32rpx;
  color: #1a1a1a;
  margin-right: 16rpx;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #9333ea;
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.nav-action-icon {
  font-size: 32rpx;
  color: #666666;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #9333ea;
  }

  &.delete-icon {
    color: #ef4444;

    &:active {
      color: #dc2626;
    }
  }
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding-bottom: 200rpx;
}

/* 物料卡片 */
.material-card {
  margin: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.image-container {
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.material-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  padding: 32rpx;
}

/* 基本信息 */
.basic-info {
  margin-bottom: 32rpx;
}

.material-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 24rpx;
  color: #9ca3af;
}

.meta-text {
  font-size: 24rpx;
  color: #64748b;
}

/* 领取条件 */
.condition-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  display: block;
}

.condition-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
}

/* 发布者信息 */
.publisher-card {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.publisher-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.publisher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.publisher-details {
  flex: 1;
}

.publisher-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}

.publisher-role {
  font-size: 24rpx;
  color: #64748b;
}

.follow-button {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.follow-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 想领统计 */
.want-section {
  margin-bottom: 32rpx;
}

.want-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.want-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.view-all-text {
  font-size: 24rpx;
  color: #9333ea;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #7c3aed;
  }
}

.want-list {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.avatar-group {
  display: flex;
  margin-right: 16rpx;
}

.want-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2px solid #ffffff;
  margin-left: -16rpx;

  &:first-child {
    margin-left: 0;
  }
}

.more-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #e5e7eb;
  border: 2px solid #ffffff;
  margin-left: -16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-count {
  font-size: 20rpx;
  color: #6b7280;
  font-weight: 600;
}

.want-count-text {
  font-size: 24rpx;
  color: #64748b;
}

.empty-want {
  text-align: center;
  padding: 32rpx 0;
}

.empty-text {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.want-button-container {
  flex: 1;
}

.want-button {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  }

  &:active {
    transform: scale(0.98);
  }
}

.want-button-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;

  .want-button.active & {
    color: #6b7280;
  }
}

.share-button {
  width: 96rpx;
  height: 96rpx;
  background: #f8fafc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: #f1f5f9;
    transform: scale(0.95);
  }
}

.share-icon {
  font-size: 32rpx;
  color: #9333ea;
}

.owner-notice {
  text-align: center;
  padding: 20rpx 0;
}

.owner-text {
  font-size: 28rpx;
  color: #64748b;
}

/* 弹窗样式 */
.modal-container {
  background: #ffffff;
  padding: 32rpx;
  max-height: 60vh;
}

.modal-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.modal-content {
  max-height: 50vh;
}

.user-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1px solid #f1f5f9;

  &:last-child {
    border-bottom: none;
  }
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}

.want-time {
  font-size: 24rpx;
  color: #64748b;
}

.follow-button-small {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.follow-text-small {
  font-size: 20rpx;
  font-weight: 600;
  color: #ffffff;
}
</style>
