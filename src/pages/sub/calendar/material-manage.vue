<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar-container" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
        <view class="nav-left">
          <text class="nav-back-icon fas fa-arrow-left" @click="goBack"></text>
          <text class="nav-title">物料管理</text>
        </view>
        <view class="nav-right">
          <view class="add-button" @click="addMaterial">
            <text class="add-text">发布</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签切换 -->
    <view class="tab-container">
      <view class="tab-switcher">
        <view class="tab-item" :class="{ active: currentTab === 'published' }" @click="switchTab('published')">
          <text class="tab-text">我发布的</text>
        </view>
        <view class="tab-item" :class="{ active: currentTab === 'wanted' }" @click="switchTab('wanted')">
          <text class="tab-text">我登记的</text>
        </view>
        <view class="tab-indicator" :class="{ 'tab-wanted': currentTab === 'wanted' }"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content-container">
      <!-- 我发布的 -->
      <view v-if="currentTab === 'published'" class="tab-content">
        <view v-if="publishedList.length === 0" class="empty-state">
          <text class="fas fa-folder-open empty-icon"></text>
          <text class="empty-title">暂无发布的物料</text>
          <text class="empty-desc">发布物料需求，让更多人看到</text>
          <view class="empty-action">
            <view class="action-button" @click="addMaterial">
              <text class="action-text">发布物料</text>
            </view>
          </view>
        </view>

        <view v-else class="material-list">
          <view v-for="item in publishedList" :key="item.id" class="material-item" @click="viewMaterial(item)">
            <view class="item-content">
              <image :src="item.image" class="item-image" />
              <view class="item-info">
                <view class="item-header">
                  <text class="item-title">{{ item.title }}</text>
                  <view class="item-actions">
                    <text class="action-icon fas fa-edit" @click.stop="editMaterial(item)"></text>
                    <text class="action-icon fas fa-trash delete-action" @click.stop="deleteMaterial(item)"></text>
                  </view>
                </view>

                <view class="item-meta">
                  <view class="meta-item">
                    <text class="fas fa-clock meta-icon"></text>
                    <text class="meta-text">{{ item.publishTime }}</text>
                  </view>
                  <view class="meta-item">
                    <text class="fas fa-map-marker-alt meta-icon"></text>
                    <text class="meta-text">{{ item.location }}</text>
                  </view>
                </view>

                <view class="item-footer">
                  <view class="footer-left">
                    <view class="want-count">
                      <text class="fas fa-heart want-icon"></text>
                      <text class="want-text">{{ item.wantCount }}人想领</text>
                    </view>
                    <view class="status-badge" :class="getStatusClass(item.status)">
                      <text class="status-text">{{ getStatusText(item.status) }}</text>
                    </view>
                  </view>
                  <text class="fas fa-chevron-right arrow-icon"></text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 我登记的 -->
      <view v-if="currentTab === 'wanted'" class="tab-content">
        <view v-if="wantedList.length === 0" class="empty-state">
          <text class="fas fa-heart empty-icon"></text>
          <text class="empty-title">暂无登记的物料</text>
          <text class="empty-desc">去日历页面看看有什么物料吧</text>
        </view>

        <view v-else class="material-list">
          <view v-for="item in wantedList" :key="item.id" class="material-item" @click="viewMaterial(item)">
            <view class="item-content">
              <image :src="item.image" class="item-image" />
              <view class="item-info">
                <view class="item-header">
                  <text class="item-title">{{ item.title }}</text>
                  <view class="cancel-want" @click.stop="cancelWant(item)">
                    <text class="cancel-text">取消登记</text>
                  </view>
                </view>

                <view class="item-meta">
                  <view class="meta-item">
                    <text class="fas fa-clock meta-icon"></text>
                    <text class="meta-text">{{ item.distributeTime }}</text>
                  </view>
                  <view class="meta-item">
                    <text class="fas fa-map-marker-alt meta-icon"></text>
                    <text class="meta-text">{{ item.location }}</text>
                  </view>
                </view>

                <view class="item-footer">
                  <view class="footer-left">
                    <text class="want-time">登记时间：{{ item.wantTime }}</text>
                  </view>
                  <view class="footer-right">
                    <text class="total-want">{{ item.totalWantCount }}人想领</text>
                    <text class="fas fa-chevron-right arrow-icon"></text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

// 状态管理
const statusBarHeight = ref(0)
const currentTab = ref<'published' | 'wanted'>('published')

// 模拟数据 - 便于后续替换为真实API
const mockPublishedData = [
  {
    id: '1',
    title: '《狮子王》限量版海报',
    image: 'https://picsum.photos/300/300?random=401',
    publishTime: '07-28 14:30',
    location: '上海大剧院门厅',
    wantCount: 26,
    status: 'active'
  },
  {
    id: '2',
    title: '音乐剧纪念品套装',
    image: 'https://picsum.photos/300/300?random=402',
    publishTime: '07-26 19:00',
    location: '剧院商店',
    wantCount: 18,
    status: 'active'
  },
  {
    id: '3',
    title: '《歌剧魅影》面具收藏版',
    image: 'https://picsum.photos/300/300?random=403',
    publishTime: '07-20 20:15',
    location: '文化广场门口',
    wantCount: 45,
    status: 'expired'
  }
]

const mockWantedData = [
  {
    id: '4',
    title: '《芝加哥》官方T恤',
    image: 'https://picsum.photos/300/300?random=404',
    distributeTime: '07-30 19:00',
    location: '天桥艺术中心',
    wantTime: '2小时前',
    totalWantCount: 32
  },
  {
    id: '5',
    title: '音乐剧主题明信片套装',
    image: 'https://picsum.photos/300/300?random=405',
    distributeTime: '08-01 14:00',
    location: '剧院门厅',
    wantTime: '1天前',
    totalWantCount: 15
  }
]

// 数据
const publishedList = ref(mockPublishedData)
const wantedList = ref(mockWantedData)

// 方法
const goBack = () => {
  uni.navigateBack()
}

const switchTab = (tab: 'published' | 'wanted') => {
  currentTab.value = tab
}

const addMaterial = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/add-material'
  })
}

const viewMaterial = (item: any) => {
  uni.navigateTo({
    url: `/pages/sub/calendar/material-detail?id=${item.id}`
  })
}

const editMaterial = (item: any) => {
  uni.navigateTo({
    url: `/pages/sub/calendar/add-material?id=${item.id}&mode=edit`
  })
}

const deleteMaterial = (item: any) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个物料信息吗？',
    success: res => {
      if (res.confirm) {
        const index = publishedList.value.findIndex(p => p.id === item.id)
        if (index > -1) {
          publishedList.value.splice(index, 1)
        }
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

const cancelWant = (item: any) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消登记这个物料吗？',
    success: res => {
      if (res.confirm) {
        const index = wantedList.value.findIndex(w => w.id === item.id)
        if (index > -1) {
          wantedList.value.splice(index, 1)
        }
        uni.showToast({
          title: '取消成功',
          icon: 'success'
        })
      }
    }
  })
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-500/20 text-green-400'
    case 'expired':
      return 'bg-gray-500/20 text-gray-400'
    case 'cancelled':
      return 'bg-red-500/20 text-red-400'
    default:
      return 'bg-gray-500/20 text-gray-400'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '进行中'
    case 'expired':
      return '已过期'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}

onLoad(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})
</script>

<style lang="scss" scoped>
/* 页面容器 */
.page-container {
  height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar-container {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-back-icon {
  font-size: 32rpx;
  color: #1a1a1a;
  margin-right: 16rpx;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #9333ea;
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.add-button {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.add-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 标签切换 */
.tab-container {
  padding: 24rpx;
}

.tab-switcher {
  position: relative;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;

  .tab-item.active & {
    color: #ffffff;
    font-weight: 600;
  }
}

.tab-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 16rpx);
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 12rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;

  &.tab-wanted {
    transform: translateX(100%);
  }
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding-bottom: 120rpx;
}

.tab-content {
  padding: 0 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 96rpx;
  color: #d1d5db;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 24rpx;
  color: #9ca3af;
  margin-bottom: 48rpx;
  display: block;
}

.empty-action {
  display: flex;
  justify-content: center;
}

.action-button {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.action-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 物料列表 */
.material-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.material-item {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  }
}

.item-content {
  display: flex;
  padding: 24rpx;
  gap: 24rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.item-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16rpx;
}

.item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  flex: 1;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
}

.action-icon {
  font-size: 28rpx;
  color: #9ca3af;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #9333ea;
  }

  &.delete-action {
    color: #ef4444;

    &:active {
      color: #dc2626;
    }
  }
}

.item-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 20rpx;
  color: #9ca3af;
}

.meta-text {
  font-size: 24rpx;
  color: #64748b;
}

.item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.want-count {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.want-icon {
  font-size: 20rpx;
  color: #ef4444;
}

.want-text {
  font-size: 24rpx;
  color: #64748b;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;

  &.active {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
  }

  &.expired {
    background: rgba(156, 163, 175, 0.1);
    color: #6b7280;
  }

  &.pending {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
  }
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
}

.arrow-icon {
  font-size: 20rpx;
  color: #9ca3af;
}

/* 取消登记按钮 */
.cancel-want {
  background: rgba(239, 68, 68, 0.1);
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(0.95);
  }
}

.cancel-text {
  font-size: 20rpx;
  font-weight: 500;
  color: #ef4444;
}

.want-time {
  font-size: 24rpx;
  color: #64748b;
}

.total-want {
  font-size: 24rpx;
  color: #9333ea;
  font-weight: 500;
}
</style>
