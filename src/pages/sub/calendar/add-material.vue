<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar-container" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
        <view class="nav-left">
          <text class="nav-back-icon fas fa-chevron-left" @click="goBack"></text>
          <text class="nav-title">{{ isEdit ? '编辑物料' : '发布物料' }}</text>
        </view>
        <view class="nav-right">
          <view class="save-button" @click="saveMaterial">
            <text class="save-text">{{ isEdit ? '保存' : '发布' }}</text>
          </view>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="content-container">
      <view class="form-container">
        <!-- 物料图片 -->
        <view class="form-section">
          <text class="section-title">物料图片 *</text>
          <view class="image-grid">
            <view v-for="(image, index) in formData.images" :key="index" class="image-item">
              <image :src="image" class="image-preview" />
              <view class="image-remove" @click="removeImage(index)">
                <text class="fas fa-times"></text>
              </view>
            </view>

            <view v-if="formData.images.length < 9" class="image-add" @click="chooseImage">
              <text class="fas fa-plus add-icon"></text>
              <text class="add-text">添加图片</text>
            </view>
          </view>
        </view>

        <!-- 物料标题 -->
        <view class="form-section">
          <text class="section-title">物料标题 *</text>
          <view class="input-wrapper">
            <input v-model="formData.title" placeholder="请输入物料标题" class="form-input" />
          </view>
        </view>

        <!-- 发放时间 -->
        <view class="form-section">
          <text class="section-title">发放时间 *</text>
          <view class="time-row">
            <view class="time-item">
              <view class="input-wrapper">
                <input v-model="formData.date" placeholder="选择日期" class="form-input" readonly @click="showDatePicker = true" />
              </view>
            </view>
            <view class="time-item">
              <view class="input-wrapper">
                <input v-model="formData.time" placeholder="选择时间" class="form-input" readonly @click="showTimePicker = true" />
              </view>
            </view>
          </view>
        </view>

        <!-- 发放地点 -->
        <view class="form-section">
          <text class="section-title">发放地点 *</text>
          <view class="input-wrapper">
            <input v-model="formData.location" placeholder="请输入发放地点" class="form-input" />
          </view>
        </view>

        <!-- 领取条件 -->
        <view class="form-section">
          <text class="section-title">领取条件 *</text>
          <view class="textarea-wrapper">
            <textarea v-model="formData.condition" placeholder="请详细描述领取条件，如：需要持有当日演出票据，每人限领一份等" class="form-textarea" maxlength="500" />
            <view class="char-count">{{ formData.condition.length }}/500</view>
          </view>
        </view>

        <!-- 关联演出 -->
        <view class="form-section">
          <text class="section-title">关联演出</text>
          <view class="select-card" @click="selectShow">
            <view v-if="formData.relatedShow" class="show-info">
              <image :src="formData.relatedShow.poster" class="show-poster" />
              <view class="show-details">
                <text class="show-title">{{ formData.relatedShow.title }}</text>
                <text class="show-date">{{ formData.relatedShow.date }}</text>
              </view>
            </view>
            <view v-else class="select-placeholder">
              <text class="placeholder-text">选择关联演出（可选）</text>
            </view>
            <text class="fas fa-chevron-right select-arrow"></text>
          </view>
        </view>

        <!-- 可见性设置 -->
        <view class="form-section">
          <text class="section-title">可见性设置</text>
          <view class="visibility-options">
            <view
              v-for="option in visibilityOptions"
              :key="option.value"
              class="visibility-option"
              :class="{ active: formData.visibility === option.value }"
              @click="formData.visibility = option.value">
              <view class="option-content">
                <text class="option-label">{{ option.label }}</text>
                <text class="option-desc">{{ option.desc }}</text>
              </view>
              <view class="radio-wrapper">
                <view class="radio-button" :class="{ checked: formData.visibility === option.value }">
                  <text v-if="formData.visibility === option.value" class="fas fa-check radio-check"></text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 日期选择器 -->
    <u-datetime-picker v-model:show="showDatePicker" v-model:value="dateValue" mode="date" @confirm="onDateConfirm" />

    <!-- 时间选择器 -->
    <u-datetime-picker v-model:show="showTimePicker" v-model:value="timeValue" mode="time" @confirm="onTimeConfirm" />
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

// 状态管理
const statusBarHeight = ref(0)
const isEdit = ref(false)
const showDatePicker = ref(false)
const showTimePicker = ref(false)
const dateValue = ref(Date.now())
const timeValue = ref(Date.now())

// 表单数据
const formData = ref({
  images: [],
  title: '',
  date: '',
  time: '',
  location: '',
  condition: '',
  relatedShow: null,
  visibility: 'public'
})

// 可见性选项
const visibilityOptions = [
  {
    value: 'public',
    label: '公开',
    desc: '所有用户都可以看到'
  },
  {
    value: 'show_audience',
    label: '同场观众',
    desc: '只有同场次观众可以看到'
  },
  {
    value: 'followers',
    label: '关注者',
    desc: '只有关注我的用户可以看到'
  }
]

// 方法
const goBack = () => {
  uni.navigateBack()
}

const chooseImage = () => {
  uni.chooseImage({
    count: 9 - formData.value.images.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      formData.value.images.push(...res.tempFilePaths)
    }
  })
}

const removeImage = (index: number) => {
  formData.value.images.splice(index, 1)
}

const onDateConfirm = (value: any) => {
  formData.value.date = value.result
  showDatePicker.value = false
}

const onTimeConfirm = (value: any) => {
  formData.value.time = value.result
  showTimePicker.value = false
}

const selectShow = () => {
  // 选择关联演出的逻辑
  uni.showToast({
    title: '选择演出功能开发中',
    icon: 'none'
  })
}

const saveMaterial = () => {
  // 表单验证
  if (!formData.value.title.trim()) {
    uni.showToast({
      title: '请输入物料标题',
      icon: 'none'
    })
    return
  }

  if (formData.value.images.length === 0) {
    uni.showToast({
      title: '请上传物料图片',
      icon: 'none'
    })
    return
  }

  // 保存逻辑
  uni.showLoading({
    title: isEdit.value ? '保存中...' : '发布中...'
  })

  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: isEdit.value ? '保存成功' : '发布成功',
      icon: 'success'
    })

    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 2000)
}

onLoad(options => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0

  if (options?.mode === 'edit') {
    isEdit.value = true
    // 加载编辑数据
  }
})
</script>

<style lang="scss" scoped>
/* 页面容器 */
.page-container {
  height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar-container {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-back-icon {
  font-size: 32rpx;
  color: #1a1a1a;
  margin-right: 16rpx;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #9333ea;
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.save-button {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.save-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding-bottom: 120rpx;
}

.form-container {
  padding: 32rpx 24rpx;
}

/* 表单区块 */
.form-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  display: block;
}

/* 图片网格 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16rpx;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e2e8f0;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-remove {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(239, 68, 68, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .fas {
    font-size: 24rpx;
    color: #ffffff;
  }
}

.image-add {
  aspect-ratio: 1;
  background: #ffffff;
  border: 2rpx dashed #d1d5db;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: #f9fafb;
    border-color: #9333ea;
  }
}

.add-icon {
  font-size: 48rpx;
  color: #9ca3af;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #6b7280;
}

/* 输入框样式 */
.input-wrapper {
  background: #ffffff;
  border-radius: 16rpx;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #9333ea;
    box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
  }
}

.form-input {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: #9ca3af;
  }
}

/* 时间选择行 */
.time-row {
  display: flex;
  gap: 16rpx;
}

.time-item {
  flex: 1;
}

/* 文本域样式 */
.textarea-wrapper {
  background: #ffffff;
  border-radius: 16rpx;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;

  &:focus-within {
    border-color: #9333ea;
    box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
  }
}

.form-textarea {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  background: transparent;
  border: none;
  outline: none;
  min-height: 200rpx;
  resize: none;

  &::placeholder {
    color: #9ca3af;
  }
}

.char-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

/* 选择卡片 */
.select-card {
  background: #ffffff;
  border-radius: 16rpx;
  border: 1px solid #e2e8f0;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: #f9fafb;
    border-color: #9333ea;
  }
}

.show-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.show-poster {
  width: 80rpx;
  height: 100rpx;
  border-radius: 8rpx;
  object-fit: cover;
  margin-right: 24rpx;
}

.show-details {
  flex: 1;
}

.show-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}

.show-date {
  font-size: 24rpx;
  color: #64748b;
}

.select-placeholder {
  flex: 1;
}

.placeholder-text {
  font-size: 28rpx;
  color: #9ca3af;
}

.select-arrow {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 可见性选项 */
.visibility-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.visibility-option {
  background: #ffffff;
  border-radius: 16rpx;
  border: 1px solid #e2e8f0;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    border-color: #9333ea;
    background: rgba(147, 51, 234, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}

.option-content {
  flex: 1;
}

.option-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #64748b;
}

.radio-wrapper {
  margin-left: 16rpx;
}

.radio-button {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.checked {
    border-color: #9333ea;
    background: #9333ea;
  }
}

.radio-check {
  font-size: 20rpx;
  color: #ffffff;
}
</style>
